/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "adc.h"
#include "dma.h"
#include "tim.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "scheduler.h"
#include "uart_app.h"
#include "adc_app.h"
#include "tlc5615.h"  // TLC5615 DAC驱动和诊断函数
#include <stdio.h>
#include <string.h>

/*
 * TLC5615调试流程说明：
 *
 * 调试模式 (TLC5615_DEBUG_MODE = 1)：
 * 阶段0: 硬件诊断测试 - 验证基本通信和输出功能
 * 阶段1: 综合诊断 - 完整的GPIO检查、硬件测试和电压计算
 * 阶段2: 波形生成测试 - 运行原有的方波和三角波生成
 * 阶段3: 持续运行模式 - 正常运行波形生成
 *
 * 正常模式 (TLC5615_DEBUG_MODE = 0)：
 * 直接运行波形生成，跳过诊断步骤
 *
 * 使用建议：
 * 1. 首次调试时使用调试模式，观察串口输出和示波器波形
 * 2. 确认DAC工作正常后，可切换到正常模式
 * 3. 如果出现0V输出问题，重新启用调试模式进行诊断
 */
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_USART1_UART_Init();
  MX_ADC1_Init();
  MX_TIM3_Init();
  /* USER CODE BEGIN 2 */
	My_FFT_Init();
	tlc5615_Init();  // 增强版初始化，包含稳定等待和通信验证
	scheduler_init();
	adc_tim_dma_init();
//	lcd_init();
//	lcd_clear(WHITE);
//	lcd_show_string(30,30,240,30,24,"   ",RED);

	// 等待串口初始化完成，确保调试输出正常
	HAL_Delay(100);

	printf("\r\n=== TLC5615 DAC 系统启动 ===\r\n");
	printf("系统初始化完成，开始TLC5615诊断...\r\n\r\n");

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */

  // 条件编译开关：调试模式 vs 正常运行模式
  #define TLC5615_DEBUG_MODE  1  // 1=调试模式，0=正常运行模式

  #if TLC5615_DEBUG_MODE
  	// 调试模式：先执行硬件诊断，再运行波形生成
  	static uint8_t debug_phase = 0;  // 调试阶段标志
  #endif

  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */

    #if TLC5615_DEBUG_MODE
    	// === 调试模式：分步测试流程 ===
    	switch(debug_phase)
    	{
    		case 0:  // 阶段0：硬件诊断测试
    			printf("=== 阶段0：硬件诊断测试 ===\r\n");
    			tlc5615_HardwareTest();  // 基础硬件测试
    			printf("硬件测试完成，请观察输出电压变化\r\n");
    			printf("如果无电压变化，请检查VREF引脚！\r\n\r\n");
    			HAL_Delay(3000);  // 3秒延时观察结果
    			debug_phase = 1;
    			break;

    		case 1:  // 阶段1：综合诊断
    			printf("=== 阶段1：综合诊断 ===\r\n");
    			tlc5615_ComprehensiveDiagnosis(5.0f);  // 假设VREF=5V
    			printf("综合诊断完成\r\n\r\n");
    			HAL_Delay(5000);  // 5秒延时查看诊断结果
    			debug_phase = 2;
    			break;

    		case 2:  // 阶段2：波形生成测试
    			printf("=== 阶段2：波形生成测试 ===\r\n");
    			printf("开始生成方波和三角波...\r\n");

    			// 原有的波形生成逻辑
    			generate_SquareWave(50, 2);   // 50个周期，每个电平持续2ms
    			generate_TriangleWave(10, 50); // 10个周期，每步延时50us

    			printf("波形生成完成\r\n\r\n");
    			HAL_Delay(2000);  // 2秒延时
    			debug_phase = 3;
    			break;

    		case 3:  // 阶段3：持续运行模式
    			// 持续运行波形生成，不再输出调试信息
    			generate_SquareWave(50, 2);
    			generate_TriangleWave(10, 50);
    			break;

    		default:
    			debug_phase = 0;  // 重置到初始阶段
    			break;
    	}

    #else
    	// === 正常运行模式：直接运行波形生成 ===
    	generate_SquareWave(50, 2);   // 测试方波：50个周期，每个电平持续2ms
    	generate_TriangleWave(10, 50); // 测试三角波：10个周期，每步延时50us
    #endif

//	scheduler_run();
  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLM = 4;
  RCC_OscInitStruct.PLL.PLLN = 168;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_5) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */
void delay_ms(uint32_t ms)
{
	uint32_t ms_uwTick=uwTick;
	
	while(uwTick-ms_uwTick<ms);
}

void Delay_us(uint32_t us) {
    uint32_t sysclk = HAL_RCC_GetHCLKFreq();  // 获取系统时钟频率 (168MHz)
    uint32_t ticks = us * (sysclk / 1000000); // 计算需要的时钟周期数
    
    SysTick->LOAD = ticks - 1;        // 设置重载值
    SysTick->VAL  = 0;                // 清除当前值
    SysTick->CTRL = SysTick_CTRL_ENABLE_Msk; // 启动SysTick
    
    // 等待计数器归零
    while ((SysTick->CTRL & SysTick_CTRL_COUNTFLAG_Msk) == 0);
    
    SysTick->CTRL = 0;  // 关闭SysTick
}
/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}
#ifdef USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
