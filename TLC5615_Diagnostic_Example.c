/*
 * TLC5615诊断功能使用示例
 * 
 * 本文件展示如何使用新开发的TLC5615硬件诊断和测试功能
 * 用于快速定位和解决0V输出问题
 */

#include "tlc5615.h"

/*
 * 示例1: 快速诊断 - 一键式完整测试
 * 适用场景: 初次调试或系统性故障排查
 */
void example_quick_diagnosis(void)
{
    printf("=== 示例1: 快速诊断 ===\r\n");
    
    // 初始化TLC5615
    tlc5615_Init();
    
    // 执行综合诊断 (假设VREF=5V)
    tlc5615_ComprehensiveDiagnosis(5.0f);
    
    printf("快速诊断完成，请查看上述输出结果\r\n\r\n");
}

/*
 * 示例2: 分步诊断 - 逐步验证各个功能
 * 适用场景: 详细调试或教学演示
 */
void example_step_by_step_diagnosis(void)
{
    printf("=== 示例2: 分步诊断 ===\r\n");
    
    // 初始化
    tlc5615_Init();
    printf("初始化完成\r\n\r\n");
    
    // 步骤1: 检查GPIO状态
    printf("步骤1: 检查GPIO状态\r\n");
    tlc5615_CheckGPIOStatus();
    HAL_Delay(1000);
    
    // 步骤2: 基础硬件测试
    printf("步骤2: 基础硬件测试\r\n");
    tlc5615_HardwareTest();
    HAL_Delay(2000);
    
    // 步骤3: 精确电压测试
    printf("步骤3: 精确电压测试\r\n");
    uint16_t test_values[] = {0, 256, 512, 768, 1023};
    
    for(int i = 0; i < 5; i++)
    {
        printf("测试数字值: %d\r\n", test_values[i]);
        tlc5615_OutputVoltage(test_values[i]);
        HAL_Delay(1000);  // 保持1秒便于观察
    }
    
    printf("分步诊断完成\r\n\r\n");
}

/*
 * 示例3: 电压计算验证
 * 适用场景: 验证理论计算和实际测量的一致性
 */
void example_voltage_calculation(void)
{
    printf("=== 示例3: 电压计算验证 ===\r\n");
    
    float vref_values[] = {2.5f, 3.3f, 5.0f};  // 常见的VREF值
    uint16_t digital_values[] = {0, 256, 512, 768, 1023};
    
    for(int v = 0; v < 3; v++)
    {
        printf("VREF = %.1fV时的电压计算:\r\n", vref_values[v]);
        
        for(int d = 0; d < 5; d++)
        {
            float expected = tlc5615_GetExpectedVoltage(digital_values[d], vref_values[v]);
            printf("  数字值 %4d -> 预期电压: %.3fV\r\n", digital_values[d], expected);
        }
        printf("\r\n");
    }
}

/*
 * 示例4: 示波器观察模式
 * 适用场景: 配合示波器进行精确测量
 */
void example_oscilloscope_mode(void)
{
    printf("=== 示例4: 示波器观察模式 ===\r\n");
    printf("请准备示波器，设置如下参数:\r\n");
    printf("- 时基: 50ms/div\r\n");
    printf("- 电压: 1V/div (假设VREF=5V)\r\n");
    printf("- 触发: 上升沿，电平1.25V\r\n\r\n");
    
    tlc5615_Init();
    
    printf("开始输出测试波形...\r\n");
    
    // 重复输出测试序列，便于示波器观察
    for(int cycle = 0; cycle < 5; cycle++)
    {
        printf("周期 %d:\r\n", cycle + 1);
        
        // 0V
        printf("  输出0V\r\n");
        tlc5615_OutputVoltage(0);
        HAL_Delay(200);
        
        // VREF/4
        printf("  输出VREF/4\r\n");
        tlc5615_OutputVoltage(256);
        HAL_Delay(200);
        
        // VREF/2
        printf("  输出VREF/2\r\n");
        tlc5615_OutputVoltage(512);
        HAL_Delay(200);
        
        // 3*VREF/4
        printf("  输出3*VREF/4\r\n");
        tlc5615_OutputVoltage(768);
        HAL_Delay(200);
        
        // VREF
        printf("  输出VREF\r\n");
        tlc5615_OutputVoltage(1023);
        HAL_Delay(200);
        
        printf("周期 %d 完成\r\n\r\n", cycle + 1);
    }
    
    // 复位到0
    tlc5615_OutputVoltage(0);
    printf("示波器观察模式完成\r\n\r\n");
}

/*
 * 示例5: 故障模拟和诊断
 * 适用场景: 教学或验证诊断功能的有效性
 */
void example_fault_simulation(void)
{
    printf("=== 示例5: 故障模拟和诊断 ===\r\n");
    
    tlc5615_Init();
    
    // 模拟不同VREF值的情况
    float vref_scenarios[] = {0.0f, 2.5f, 3.3f, 5.0f};
    const char* scenario_names[] = {"VREF故障(0V)", "VREF=2.5V", "VREF=3.3V", "VREF=5.0V"};
    
    for(int i = 0; i < 4; i++)
    {
        printf("场景 %d: %s\r\n", i+1, scenario_names[i]);
        
        // 输出中间值
        tlc5615_OutputVoltage(512);
        
        // 计算预期电压
        float expected = tlc5615_GetExpectedVoltage(512, vref_scenarios[i]);
        printf("数字值512的预期输出: %.3fV\r\n", expected);
        
        if(vref_scenarios[i] == 0.0f)
        {
            printf("⚠️  警告: VREF=0V时，输出将始终为0V！\r\n");
            printf("   这是0V输出问题的最常见原因\r\n");
        }
        
        printf("\r\n");
        HAL_Delay(1000);
    }
}

/*
 * 主诊断函数 - 可以在main函数中调用
 */
void run_tlc5615_diagnostic_examples(void)
{
    printf("\r\n");
    printf("##########################################\r\n");
    printf("#     TLC5615 诊断功能演示开始          #\r\n");
    printf("##########################################\r\n\r\n");
    
    // 运行所有示例
    example_quick_diagnosis();
    HAL_Delay(2000);
    
    example_step_by_step_diagnosis();
    HAL_Delay(2000);
    
    example_voltage_calculation();
    HAL_Delay(2000);
    
    example_oscilloscope_mode();
    HAL_Delay(2000);
    
    example_fault_simulation();
    
    printf("##########################################\r\n");
    printf("#     TLC5615 诊断功能演示完成          #\r\n");
    printf("##########################################\r\n\r\n");
}

/*
 * 在main.c中的使用方法:
 * 
 * int main(void)
 * {
 *     // 系统初始化
 *     HAL_Init();
 *     SystemClock_Config();
 *     MX_GPIO_Init();
 *     MX_USART1_UART_Init();
 *     
 *     // 运行TLC5615诊断
 *     run_tlc5615_diagnostic_examples();
 *     
 *     while(1)
 *     {
 *         // 正常程序逻辑
 *     }
 * }
 */
