#include "tlc5615.h" 

/*TLC5615的SPI通信模式为Mode0 : CPOL = 0, CPHA = 0*/

/*
*********************************************************************************************************
*	函数名: tlc5615_Init
*	功能说明: 初始化TLC5615 DAC，包含上电稳定等待和基本通信验证
*	形    参: 无
*	返 回 值: 无
*	说    明: 增强版初始化，确保DAC在上电后正确工作，解决0V输出问题
*********************************************************************************************************
*/
void tlc5615_Init(void)
{
	// 1. 设置GPIO初始状态
	SCK_0();	// 时钟信号初始为低电平 (SPI Mode 0)
	CS_1();		// 片选信号初始为高电平 (未选中)
	DI_0();		// 数据线初始为低电平

	// 2. 等待上电稳定 - 关键步骤，确保DAC内部电路稳定
	HAL_Delay(10);  // 10ms稳定时间，根据TLC5615数据手册建议

	// 3. 发送测试值验证通信 - 输出中间值测试DAC响应
	tlc5615_Send12Bit(TLC5615_MID_VALUE);  // 发送512，应输出约一半VREF电压
	HAL_Delay(1);   // 短暂延时确保数据传输完成

	// 4. 复位到初始状态 - 避免意外输出
	tlc5615_Send12Bit(TLC5615_MIN_VALUE);  // 复位到0值

	// 初始化完成，DAC现在应该能够正常响应后续命令
}

/*
*********************************************************************************************************
*	函数名: tlc5615_InitTest
*	功能说明: 测试TLC5615初始化后的基本功能，验证DAC是否正常工作
*	形    参: 无
*	返 回 值: 无
*	说    明: 通过输出几个测试值来验证DAC响应，帮助诊断初始化是否成功
*********************************************************************************************************
*/
void tlc5615_InitTest(void)
{
	printf("=== TLC5615初始化测试开始 ===\r\n");

	// 测试序列：0V -> 中间值 -> 最大值 -> 0V
	printf("输出0V (数字值: %d)\r\n", TLC5615_MIN_VALUE);
	tlc5615_Send12Bit(TLC5615_MIN_VALUE);
	HAL_Delay(500);  // 500ms延时便于观察

	printf("输出中间值 (数字值: %d)\r\n", TLC5615_MID_VALUE);
	tlc5615_Send12Bit(TLC5615_MID_VALUE);
	HAL_Delay(500);

	printf("输出最大值 (数字值: %d)\r\n", TLC5615_MAX_VALUE);
	tlc5615_Send12Bit(TLC5615_MAX_VALUE);
	HAL_Delay(500);

	printf("复位到0V (数字值: %d)\r\n", TLC5615_MIN_VALUE);
	tlc5615_Send12Bit(TLC5615_MIN_VALUE);

	printf("=== 初始化测试完成 ===\r\n");
	printf("如果DAC工作正常，应该能观察到电压变化\r\n");
}

/*
*********************************************************************************************************
*	�� �� ��: tlc5615_Send8Bit
*	����˵��: ��SPI���߷���8��bit���ݡ� ����CS���ơ�
*	��    ��: _data : ����
*	�� �� ֵ: ��
*********************************************************************************************************
*/ 
void tlc5615_Send8Bit(uint8_t _data)
{
	uint8_t i; 
	
	for(i = 0; i < 8; i++)
	{
		if (_data & 0x80)
		{
			DI_1();
		}
		else
		{
			DI_0();
		}
		SCK_1();
		_data <<= 1;
		Delay_us(1);
		SCK_0();	
		Delay_us(1);		
	}
}

/*
*********************************************************************************************************
*	�� �� ��: tlc5615_WriteByte
*	����˵��: д��1���ֽڡ���CS����
*	��    ��: _data ����Ҫд�������
*	�� �� ֵ: ��
*********************************************************************************************************
*/
void tlc5615_WriteByte(uint8_t _data)
{
	CS_0();
	tlc5615_Send8Bit(_data);
	CS_1();
}

/*
*********************************************************************************************************
*	�� �� ��: tlc5615_Write2Byte
*	����˵��: д��2���ֽڡ���CS����
*	��    ��: _data ����Ҫд�������
*	�� �� ֵ: ��
*********************************************************************************************************
*/
void tlc5615_Write2Byte(uint16_t _data)
{
	CS_0(); 
	tlc5615_Send8Bit((_data >> 8) & 0xFF);
	tlc5615_Send8Bit(_data);
	CS_1();
} 

/*
*********************************************************************************************************
*	函数名: tlc5615_Send12Bit
*	功能说明: 向SPI总线发送12个bit数据。包含CS操作。
*	形    参: _data : 数据，范围 0 ~ 1023 (10位DAC)
*	返 回 值: 无
*********************************************************************************************************
*/
void tlc5615_Send12Bit(uint16_t _data)	
{
	uint8_t i;

	_data = _data << 6;		/*����6λ�󣬴�ʱ���λΪMSB*/
	
	CS_0();
	
	for(i = 0; i < 12; i++)
	{
		if (_data & 0x8000)
		{
			DI_1();
		}
		else
		{
			DI_0();
		}
		SCK_1();
		_data <<= 1;
		Delay_us(1);
		SCK_0();	
		Delay_us(1);	
	}
	
	CS_1();
}  

void generate_Wave(void)
{
	uint16_t n=0, j=0;
	uint16_t wave_max = 1023;  // TLC5615最大值(10位DAC)
	uint16_t wave_min = 0;     // 最小值

	while(1)
	{
		// 生成方波 (0V到5V)
		for(n=0; n<50; n++)	 	// 50次方波周期
		{
		   tlc5615_Send12Bit(wave_min);    // 0V
		   delay_ms(1);                    // 1ms延时
		   tlc5615_Send12Bit(wave_max);    // 5V
		   delay_ms(1);                    // 1ms延时
		}

		// 生成三角波
		for(n=0; n<10; n++)		// 10次三角波周期
		{
			// 上升沿：从0到1023
			for(j=wave_min; j<=wave_max; j+=8)
			{
			   tlc5615_Send12Bit(j);
			   Delay_us(50);  // 50us延时，更快的波形
			}
			// 下降沿：从1023到0
			for(j=wave_max; j>wave_min; j-=8)
			{
				tlc5615_Send12Bit(j);
				Delay_us(50);  // 50us延时
			}
		}
	}
}

// 生成方波函数
void generate_SquareWave(uint16_t cycles, uint16_t ms)
{
	uint16_t i;
	for(i=0; i<cycles; i++)
	{
		tlc5615_Send12Bit(0);      // 输出0V
		delay_ms(ms);
		tlc5615_Send12Bit(1023);   // 输出5V (假设Vref=5V)
		delay_ms(ms);
	}
}

// 生成三角波函数
void generate_TriangleWave(uint16_t cycles, uint16_t delay_us)
{
	uint16_t i, j;
	for(i=0; i<cycles; i++)
	{
		// 上升沿：从0到1023
		for(j=0; j<=1023; j+=8)
		{
			tlc5615_Send12Bit(j);
			Delay_us(delay_us);
		}
		// 下降沿：从1023到0
		for(j=1023; j>0; j-=8)
		{
			tlc5615_Send12Bit(j);
			Delay_us(delay_us);
		}
	}
}

/*
*********************************************************************************************************
*	函数名: tlc5615_CheckGPIOStatus
*	功能说明: 检查TLC5615相关GPIO引脚的当前状态，用于硬件连接诊断
*	形    参: 无
*	返 回 值: 无
*	说    明: 通过串口输出GPIO状态信息，帮助诊断硬件连接问题
*********************************************************************************************************
*/
void tlc5615_CheckGPIOStatus(void)
{
	// 读取当前GPIO状态
	GPIO_PinState sck_state = HAL_GPIO_ReadPin(PORT_SCK, PIN_SCK);
	GPIO_PinState din_state = HAL_GPIO_ReadPin(PORT_DIN, PIN_DIN);
	GPIO_PinState cs_state = HAL_GPIO_ReadPin(PORT_CS, PIN_CS);
	GPIO_PinState dout_state = HAL_GPIO_ReadPin(PORT_DOUT, PIN_DOUT);

	// 输出状态信息(需要串口支持)
	printf("=== TLC5615 GPIO状态检查 ===\r\n");
	printf("SCK (PD3): %s\r\n", sck_state == GPIO_PIN_SET ? "HIGH" : "LOW");
	printf("DIN (PD2): %s\r\n", din_state == GPIO_PIN_SET ? "HIGH" : "LOW");
	printf("CS  (PD4): %s\r\n", cs_state == GPIO_PIN_SET ? "HIGH" : "LOW");
	printf("DOUT(PD5): %s\r\n", dout_state == GPIO_PIN_SET ? "HIGH" : "LOW");
	printf("===========================\r\n");

	// 检查预期状态
	printf("预期状态检查:\r\n");
	printf("CS应为HIGH: %s\r\n", cs_state == GPIO_PIN_SET ? "✓正常" : "");
	printf("SCK应为LOW: %s\r\n", sck_state == GPIO_PIN_RESET ? "✓正常" : "✗异常");
}

/*
*********************************************************************************************************
*	函数名: tlc5615_HardwareTest
*	功能说明: 硬件测试函数，依次输出0、512、1023值，便于示波器观察
*	形    参: 无
*	返 回 值: 无
*	说    明: 每个值保持100ms，适合示波器观察波形变化
*********************************************************************************************************
*/
void tlc5615_HardwareTest(void)
{
	printf("=== TLC5615硬件测试开始 ===\r\n");
	printf("测试序列: 0V -> 中间值 -> 最大值\r\n");
	printf("每个值保持100ms，请使用示波器观察\r\n\r\n");

	// 测试1: 输出最小值 (0V)
	printf("1. 输出最小值 (数字值: %d) - 预期: 0V\r\n", TLC5615_MIN_VALUE);
	tlc5615_Send12Bit(TLC5615_MIN_VALUE);
	HAL_Delay(100);  // 100ms延时便于示波器观察

	// 测试2: 输出中间值 (约VREF/2)
	printf("2. 输出中间值 (数字值: %d) - 预期: VREF/2\r\n", TLC5615_MID_VALUE);
	tlc5615_Send12Bit(TLC5615_MID_VALUE);
	HAL_Delay(100);

	// 测试3: 输出最大值 (约VREF)
	printf("3. 输出最大值 (数字值: %d) - 预期: VREF\r\n", TLC5615_MAX_VALUE);
	tlc5615_Send12Bit(TLC5615_MAX_VALUE);
	HAL_Delay(100);

	// 复位到0
	printf("4. 复位到0V\r\n");
	tlc5615_Send12Bit(TLC5615_MIN_VALUE);

	printf("=== 硬件测试完成 ===\r\n");
	printf("如果VREF正常，应该能观察到电压变化\r\n\r\n");
}

/*
*********************************************************************************************************
*	函数名: tlc5615_OutputVoltage
*	功能说明: 直接设置指定的数字值输出，便于精确测试
*	形    参: digital_value: 数字值 (0-1023)
*	返 回 值: 无
*	说    明: 输入范围检查，超出范围自动限制
*********************************************************************************************************
*/
void tlc5615_OutputVoltage(uint16_t digital_value)
{
	// 范围检查，确保输入值在有效范围内
	if (digital_value > TLC5615_MAX_VALUE)
	{
		digital_value = TLC5615_MAX_VALUE;
		printf("警告: 输入值超出范围，已限制为最大值 %d\r\n", TLC5615_MAX_VALUE);
	}

	// 输出数字值
	tlc5615_Send12Bit(digital_value);

	// 输出调试信息
	printf("设置数字值: %d (预期电压: %.3fV，假设VREF=5V)\r\n",
		   digital_value, (float)digital_value * 5.0f / 1023.0f);
}

/*
*********************************************************************************************************
*	函数名: tlc5615_GetExpectedVoltage
*	功能说明: 根据数字值和参考电压计算预期输出电压
*	形    参: digital_value: 数字值 (0-1023)
*           vref: 参考电压值 (V)
*	返 回 值: 预期输出电压 (V)
*	说    明: 计算公式: Vout = (digital_value / 1023) × VREF
*********************************************************************************************************
*/
float tlc5615_GetExpectedVoltage(uint16_t digital_value, float vref)
{
	// 范围检查
	if (digital_value > TLC5615_MAX_VALUE)
	{
		digital_value = TLC5615_MAX_VALUE;
	}

	// 计算预期电压: Vout = (digital_value / 1023) × VREF
	float expected_voltage = ((float)digital_value / (float)TLC5615_MAX_VALUE) * vref;

	return expected_voltage;
}

/*
*********************************************************************************************************
*	函数名: tlc5615_ComprehensiveDiagnosis
*	功能说明: 综合诊断函数，执行完整的硬件和功能测试
*	形    参: vref: 系统的参考电压值 (V)
*	返 回 值: 无
*	说    明: 结合GPIO检查、硬件测试和电压计算的完整诊断流程
*********************************************************************************************************
*/
void tlc5615_ComprehensiveDiagnosis(float vref)
{
	printf("\r\n");
	printf("========================================\r\n");
	printf("    TLC5615 综合诊断测试开始\r\n");
	printf("========================================\r\n");
	printf("参考电压VREF: %.2fV\r\n\r\n", vref);

	// 1. GPIO状态检查
	printf("--- 步骤1: GPIO状态检查 ---\r\n");
	tlc5615_CheckGPIOStatus();
	printf("\r\n");

	// 2. 硬件功能测试
	printf("--- 步骤2: 硬件功能测试 ---\r\n");
	tlc5615_HardwareTest();

	// 3. 电压计算验证
	printf("--- 步骤3: 电压计算验证 ---\r\n");
	uint16_t test_values[] = {0, 256, 512, 768, 1023};
	uint8_t test_count = sizeof(test_values) / sizeof(test_values[0]);

	for (uint8_t i = 0; i < test_count; i++)
	{
		float expected = tlc5615_GetExpectedVoltage(test_values[i], vref);
		printf("数字值 %4d -> 预期电压: %.3fV\r\n", test_values[i], expected);
	}

	printf("\r\n");
	printf("========================================\r\n");
	printf("    综合诊断测试完成\r\n");
	printf("========================================\r\n");
	printf("诊断建议:\r\n");
	printf("1. 如果GPIO状态异常，检查硬件连接\r\n");
	printf("2. 如果无电压变化，重点检查VREF引脚\r\n");
	printf("3. 如果电压不准确，校准VREF电压值\r\n");
	printf("4. 使用示波器观察输出波形更准确\r\n\r\n");
}
