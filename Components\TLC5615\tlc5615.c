#include "tlc5615.h" 

/*TLC5615��SPIͨ��ģʽΪMode0 : CPOL = 0, CPHA = 0*/

void tlc5615_Init(void)
{
	SCK_0();	CS_1();		/*����SCLKΪ0*/
	
} 

/*
*********************************************************************************************************
*	�� �� ��: tlc5615_Send8Bit
*	����˵��: ��SPI���߷���8��bit���ݡ� ����CS���ơ�
*	��    ��: _data : ����
*	�� �� ֵ: ��
*********************************************************************************************************
*/ 
void tlc5615_Send8Bit(uint8_t _data)
{
	uint8_t i; 
	
	for(i = 0; i < 8; i++)
	{
		if (_data & 0x80)
		{
			DI_1();
		}
		else
		{
			DI_0();
		}
		SCK_1();
		_data <<= 1;
		Delay_us(1);
		SCK_0();	
		Delay_us(1);		
	}
}

/*
*********************************************************************************************************
*	�� �� ��: tlc5615_WriteByte
*	����˵��: д��1���ֽڡ���CS����
*	��    ��: _data ����Ҫд�������
*	�� �� ֵ: ��
*********************************************************************************************************
*/
void tlc5615_WriteByte(uint8_t _data)
{
	CS_0();
	tlc5615_Send8Bit(_data);
	CS_1();
}

/*
*********************************************************************************************************
*	�� �� ��: tlc5615_Write2Byte
*	����˵��: д��2���ֽڡ���CS����
*	��    ��: _data ����Ҫд�������
*	�� �� ֵ: ��
*********************************************************************************************************
*/
void tlc5615_Write2Byte(uint16_t _data)
{
	CS_0(); 
	tlc5615_Send8Bit((_data >> 8) & 0xFF);
	tlc5615_Send8Bit(_data);
	CS_1();
} 

/*
*********************************************************************************************************
*	函数名: tlc5615_Send12Bit
*	功能说明: 向SPI总线发送12个bit数据。包含CS操作。
*	形    参: _data : 数据，范围 0 ~ 1023 (10位DAC)
*	返 回 值: 无
*********************************************************************************************************
*/
void tlc5615_Send12Bit(uint16_t _data)	
{
	uint8_t i;

	_data = _data << 6;		/*����6λ�󣬴�ʱ���λΪMSB*/
	
	CS_0();
	
	for(i = 0; i < 12; i++)
	{
		if (_data & 0x8000)
		{
			DI_1();
		}
		else
		{
			DI_0();
		}
		SCK_1();
		_data <<= 1;
		Delay_us(1);
		SCK_0();	
		Delay_us(1);	
	}
	
	CS_1();
}  

void generate_Wave(void)
{
	uint16_t n=0, j=0;
	uint16_t wave_max = 1023;  // TLC5615最大值(10位DAC)
	uint16_t wave_min = 0;     // 最小值

	while(1)
	{
		// 生成方波 (0V到5V)
		for(n=0; n<50; n++)	 	// 50次方波周期
		{
		   tlc5615_Send12Bit(wave_min);    // 0V
		   delay_ms(1);                    // 1ms延时
		   tlc5615_Send12Bit(wave_max);    // 5V
		   delay_ms(1);                    // 1ms延时
		}

		// 生成三角波
		for(n=0; n<10; n++)		// 10次三角波周期
		{
			// 上升沿：从0到1023
			for(j=wave_min; j<=wave_max; j+=8)
			{
			   tlc5615_Send12Bit(j);
			   Delay_us(50);  // 50us延时，更快的波形
			}
			// 下降沿：从1023到0
			for(j=wave_max; j>wave_min; j-=8)
			{
				tlc5615_Send12Bit(j);
				Delay_us(50);  // 50us延时
			}
		}
	}
}

// 生成方波函数
void generate_SquareWave(uint16_t cycles, uint16_t ms)
{
	uint16_t i;
	for(i=0; i<cycles; i++)
	{
		tlc5615_Send12Bit(0);      // 输出0V
		delay_ms(ms);
		tlc5615_Send12Bit(1023);   // 输出5V (假设Vref=5V)
		delay_ms(ms);
	}
}

// 生成三角波函数
void generate_TriangleWave(uint16_t cycles, uint16_t delay_us)
{
	uint16_t i, j;
	for(i=0; i<cycles; i++)
	{
		// 上升沿：从0到1023
		for(j=0; j<=1023; j+=8)
		{
			tlc5615_Send12Bit(j);
			Delay_us(delay_us);
		}
		// 下降沿：从1023到0
		for(j=1023; j>0; j-=8)
		{
			tlc5615_Send12Bit(j);
			Delay_us(delay_us);
		}
	}
}
