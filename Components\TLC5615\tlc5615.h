#ifndef __TLC5615_H__
#define __TLC5615_H__

#include "mydefine.h"

/* 定义GPIO端口 */
#define PORT_SCK	GPIOD
#define PIN_SCK		GPIO_PIN_3

#define PORT_DIN	GPIOD
#define PIN_DIN		GPIO_PIN_2

#define PORT_DOUT	GPIOD
#define PIN_DOUT	GPIO_PIN_5

#define PORT_CS		GPIOD
#define PIN_CS		GPIO_PIN_4


/* 定义口线置0和置1的宏（HAL库版本） */
#define CS_0()      HAL_GPIO_WritePin(PORT_CS, PIN_CS, GPIO_PIN_RESET)
#define CS_1()      HAL_GPIO_WritePin(PORT_CS, PIN_CS, GPIO_PIN_SET)

#define SCK_0()     HAL_GPIO_WritePin(PORT_SCK, PIN_SCK, GPIO_PIN_RESET)
#define SCK_1()     HAL_GPIO_WritePin(PORT_SCK, <PERSON>IN_SCK, GPIO_PIN_SET)

#define DI_0()      HAL_GPIO_WritePin(PORT_DIN, PIN_DIN, GPIO_PIN_RESET)
#define DI_1()      HAL_GPIO_WritePin(PORT_DIN, PIN_DIN, GPIO_PIN_SET)

#define DO_IS_HIGH() (HAL_GPIO_ReadPin(PORT_DOUT, PIN_DOUT) == GPIO_PIN_SET)

void tlc5615_Init(void);
void tlc5615_Send8Bit(uint8_t _data);
void tlc5615_WriteByte(uint8_t _data);
void tlc5615_Write2Byte(uint16_t _data);
void tlc5615_Send12Bit(uint16_t _data);
void generate_Wave(void);
void generate_SquareWave(uint16_t cycles, uint16_t delay_ms);  // 生成方波
void generate_TriangleWave(uint16_t cycles, uint16_t delay_us); // 生成三角波


#endif
 









































