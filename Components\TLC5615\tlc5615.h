#ifndef __TLC5615_H__
#define __TLC5615_H__

#include "mydefine.h"

/*
 * TLC5615 10位DAC硬件连接说明
 * 重要: 输出电压 = (数字值/1023) × VREF
 * 如果输出为0V，首先检查VREF引脚电压！
 *
 * 电源要求:
 * - VDD: 5.0V ± 5% (4.75V - 5.25V)
 * - VSS: 0V (接地)
 * - VREF: 参考电压 (通常2.5V或5V)
 *
 * STM32F407ZGT6连接:
 * - PD2 → TLC5615 DIN  (数据输入)
 * - PD3 → TLC5615 SCK  (时钟)
 * - PD4 → TLC5615 CS   (片选)
 * - PD5 ← TLC5615 DOUT (数据输出，可选)
 */

/* 定义GPIO端口 */
#define PORT_SCK	GPIOD
#define PIN_SCK		GPIO_PIN_3

#define PORT_DIN	GPIOD
#define PIN_DIN		GPIO_PIN_2

#define PORT_DOUT	GPIOD
#define PIN_DOUT	GPIO_PIN_5

#define PORT_CS		GPIOD
#define PIN_CS		GPIO_PIN_4


/* 定义口线置0和置1的宏（HAL库版本） */
#define CS_0()      HAL_GPIO_WritePin(PORT_CS, PIN_CS, GPIO_PIN_RESET)
#define CS_1()      HAL_GPIO_WritePin(PORT_CS, PIN_CS, GPIO_PIN_SET)

#define SCK_0()     HAL_GPIO_WritePin(PORT_SCK, PIN_SCK, GPIO_PIN_RESET)
#define SCK_1()     HAL_GPIO_WritePin(PORT_SCK, PIN_SCK, GPIO_PIN_SET)

#define DI_0()      HAL_GPIO_WritePin(PORT_DIN, PIN_DIN, GPIO_PIN_RESET)
#define DI_1()      HAL_GPIO_WritePin(PORT_DIN, PIN_DIN, GPIO_PIN_SET)

#define DO_IS_HIGH() (HAL_GPIO_ReadPin(PORT_DOUT, PIN_DOUT) == GPIO_PIN_SET)

/* 硬件检查相关常量 */
#define TLC5615_MAX_VALUE       1023    // 10位DAC最大值
#define TLC5615_MID_VALUE       512     // 中间值，用于测试
#define TLC5615_MIN_VALUE       0       // 最小值
#define TLC5615_VDD_NOMINAL     5.0f    // 标称VDD电压(V)
#define TLC5615_VDD_TOLERANCE   0.25f   // VDD容差(±5%)

/* 函数声明 */
void tlc5615_Init(void);
void tlc5615_Send8Bit(uint8_t _data);
void tlc5615_WriteByte(uint8_t _data);
void tlc5615_Write2Byte(uint16_t _data);
void tlc5615_Send12Bit(uint16_t _data);
void generate_Wave(void);
void generate_SquareWave(uint16_t cycles, uint16_t delay_ms);  // 生成方波
void generate_TriangleWave(uint16_t cycles, uint16_t delay_us); // 生成三角波

/* 硬件诊断函数 */
void tlc5615_CheckGPIOStatus(void);  // 检查GPIO状态
void tlc5615_InitTest(void);         // 初始化功能测试


#endif
 









































